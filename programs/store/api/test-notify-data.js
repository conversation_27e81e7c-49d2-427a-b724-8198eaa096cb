export default async function (app, store, request, response) {
    try {
        const user = await app.collection('kernel.users').findOne({ isRoot: true });
        
        // Get sample data
        const stores = await app.collection('ecommerce.stores').find({ $limit: 1 });
        const customers = await app.collection('kernel.partners').find({ type: 'customer', $limit: 2 });
        const products = await app.collection('inventory.products').find({ type: 'stockable', $limit: 5 });
        
        if (stores.length === 0 || customers.length === 0 || products.length === 0) {
            return response.status(400).json({
                error: 'Not enough sample data',
                stores: stores.length,
                customers: customers.length,
                products: products.length
            });
        }
        
        // Create test notify records
        const testRecords = [];
        
        // Create multiple requests for same products from different customers
        for (let i = 0; i < products.length; i++) {
            const product = products[i];
            
            // Customer 1 requests this product
            if (customers[0]) {
                testRecords.push({
                    storeId: stores[0]._id,
                    customerId: customers[0]._id,
                    productId: product._id,
                    isNotifyCustomer: true,
                    isBuyLater: false,
                    isAlarm: false
                });
            }
            
            // Customer 2 requests some products too
            if (customers[1] && i < 3) {
                testRecords.push({
                    storeId: stores[0]._id,
                    customerId: customers[1]._id,
                    productId: product._id,
                    isNotifyCustomer: true,
                    isBuyLater: false,
                    isAlarm: false
                });
            }
        }
        
        // Check if records already exist
        const existing = await app.collection('store.collection-products').count({
            isNotifyCustomer: true
        });
        
        if (existing > 0) {
            return response.json({
                message: 'Test data already exists',
                existingRecords: existing
            });
        }
        
        // Create the records
        const created = await app.collection('store.collection-products').create(testRecords, { user });
        
        return response.json({
            message: 'Test data created successfully',
            recordsCreated: Array.isArray(created) ? created.length : 1,
            sampleData: {
                store: stores[0].name,
                customers: customers.map(c => c.name),
                products: products.map(p => p.name)
            }
        });
        
    } catch (error) {
        console.error('Error creating test data:', error);
        return response.status(500).json({
            error: 'Failed to create test data',
            message: error.message
        });
    }
}
