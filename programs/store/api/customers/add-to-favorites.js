import {ObjectId} from 'mongodb';

export default async function (app, store, request, response) {
    const {customerId, productId: favoriteProductId} = request.body;

    if (!customerId) {
        return response.status(422).json({
            status: 'error',
            code: 'customer_id_is_required',
            message: 'Customer ID is required'
        });
    }

    if (!favoriteProductId) {
        return response.status(422).json({
            status: 'error',
            code: 'product_id_is_required',
            message: 'Product ID is required'
        });
    }

    const user = await app.collection('kernel.users').findOne({isRoot: true});

    const product = await app.collection('store.products').findOne({
        storeId: store._id,
        $or: [{productId: favoriteProductId}, {'variants.productId': favoriteProductId}],
        $select: ['productId', 'favoritesCount', 'variants']
    });
    let mainProductId = null;
    let productId = null;
    if (!product) {
        return response.status(404).json({
            status: 'error',
            code: 'product_not_found',
            message: 'Product not found'
        });
    }
    if (Array.isArray(product.variants) && product.variants.length > 0) {
        const subProduct = product.variants.find(variant => variant.productId === favoriteProductId);

        if (!!subProduct) {
            mainProductId = product.productId;
            productId = subProduct.productId;
        }
    }
    if (!productId && product.productId === favoriteProductId) {
        productId = product.productId;

        if (Array.isArray(product.variants) && product.variants.length > 0) {
            const subProduct = product.variants[0];

            mainProductId = product.productId;
            productId = subProduct.productId;
        }
    }
    if (!productId) {
        return response.status(422).json({
            status: 'error',
            code: 'product_not_found',
            message: 'Product not found'
        });
    }

    const existing = await app.collection('store.favorite-products').findOne({
        storeId: store._id,
        customerId,
        productId
    });
    if (!existing) {
        const payload = {
            favoritesCount: product.favoritesCount + 1
        };
        if (Array.isArray(product.variants) && product.variants.length > 0) {
            payload.variants = product.variants.map(variant => {
                if (variant.productId === productId) {
                    variant.favoritesCount += 1;
                }

                return variant;
            });
        }

        await app.db.collection('store_products').updateOne(
            {_id: new ObjectId(product._id)},
            {
                $set: payload
            },
            {
                collation: {locale: app.config('app.locale')}
            }
        );
        await app.collection('store.favorite-products').create(
            {
                storeId: store._id,
                customerId,
                productId,
                ...(!!mainProductId ? {mainProductId} : {})
            },
            {user}
        );
    }

    return response.status(200).json({});
}
