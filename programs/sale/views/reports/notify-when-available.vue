<template>
    <ui-view
        type="content"
        :title="'Notify When Available' | t"
        :left-panel-width="270"
        class="sale-reports-notify-when-available"
        v-if="initialized"
    >
        <template slot="left-panel" v-if="productIds.length < 1 && module !== 'ecommerce'">
            <div class="sale-reports-notify-when-available-categories">
                <div class="category-search">
                    <el-input
                        v-model="categorySearchQuery"
                        :placeholder="'Search category..' | t"
                        prefix-icon="el-icon-search"
                        autocorrect="off"
                        autocapitalize="off"
                        spellcheck="false"
                        clearable
                        size="medium"
                        @input="categorySearchQuery = $event"
                    />
                </div>
                <ui-table
                    ref="table"
                    :items="categories"
                    :columns="categoriesColumns"
                    :search="categorySearchQuery"
                    :enable-row-handle="false"
                    no-zebra
                    :options="categoriesTableOptions"
                    @selected="handleSelectCategory"
                    v-if="categories.length > 0"
                />
            </div>
        </template>

        <template slot="top-panel">
            <ui-scope
                ref="scope"
                id="sale.reports.notify-when-available"
                :applied-items="appliedScopeItems"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
            />
        </template>

        <ui-table
            ref="table"
            :key="tableKey"
            :id="`sale.reports.notify-when-available`"
            row-model="serverSide"
            :columns="columns"
            :filters="filters"
            :get-rows="getRows"
            :summary-row="summaryRow"
            :enable-sorting="false"
            :enable-selection="false"
        />
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {escapeRegExp} from 'framework/helpers';

export default {
    name: 'sale-reports-notify-when-available',
    data() {
        return {
            initialized: false,
            tableKey: 0,
            categorySearchQuery: '',
            categories: [],
            categoryFilters: {},
            isFiltering: false,
            rowCount: 0
        };
    },
    computed: {
        module() {
            return this.$route.params.module || 'sale';
        },
        productIds() {
            return this.$route.params.productIds || [];
        },
        payload() {
            const payload = {};

            if (this.productIds.length > 0) {
                payload.query = {productId: {$in: this.productIds}};
            }

            return payload;
        },
        filters() {
            return this.$route.params.filters || {};
        },
        scopeApplicableFilters() {
            const filters = [
                {
                    type: 'date-range',
                    name: 'date',
                    label: 'Date',
                    startDateField: 'startDate',
                    endDateField: 'endDate'
                }
            ];

            if (this.module === 'ecommerce') {
                filters.push({
                    type: 'select',
                    name: 'storeId',
                    label: 'Store',
                    collection: 'ecommerce.stores',
                    valueField: '_id',
                    labelField: 'name',
                    multiple: true
                });
            }

            return filters;
        },
        columns() {
            return [
                {
                    field: 'productCode',
                    label: 'Product code',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data) {
                            return {
                                id: data.productId,
                                view: 'inventory.catalog.products'
                            };
                        }
                    },
                    width: 150
                },
                {
                    field: 'productDefinition',
                    label: 'Product definition',
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.productId,
                            view: 'inventory.catalog.products-detail'
                        };
                    },
                    minWidth: 150
                },
                {
                    field: 'productType',
                    label: 'Product type',
                    visible: false,
                    width: 150,
                    valueLabels: [
                        {value: 'stockable', label: 'Stockable product'},
                        {value: 'service', label: 'Service product'}
                    ],
                    translateLabels: true
                },
                {
                    field: 'notifyRequestsCount',
                    label: 'Notify requests count',
                    width: 150,
                    format: 'decimal'
                },
                {
                    field: 'uniqueCustomersCount',
                    label: 'Unique customers',
                    width: 150,
                    format: 'decimal'
                },
                {
                    field: 'storeCount',
                    label: 'Store count',
                    width: 120,
                    format: 'decimal'
                },
                {
                    field: 'firstRequestDate',
                    label: 'First request date',
                    width: 150,
                    format: 'date'
                },
                {
                    field: 'lastRequestDate',
                    label: 'Last request date',
                    width: 150,
                    format: 'date'
                }
            ];
        },
        appliedScopeItems() {
            return [];
        },
        categoriesColumns() {
            return [
                {
                    field: 'name',
                    label: 'Category name',
                    minWidth: 150
                }
            ];
        },
        categoriesTableOptions() {
            return {
                enableSelection: true,
                enableSorting: false,
                enableFiltering: false,
                enablePagination: false,
                enableColumnMenu: false,
                enableRowHandle: false,
                enableRowMenu: false,
                enableRowSelection: true,
                enableMultipleSelection: true
            };
        }
    },

    async created() {
        this.refreshForRealTime = _.debounce(this.refresh, 300, {leading: false, trailing: true});

        this.categories = await this.$collection('inventory.product-categories').find();
        this.module = (this.$params('filters') || {}).module || 'sale';

        this.$collection('store.collection-products').on('all', this.refreshForRealTime);

        this.initialized = true;
    },

    methods: {
        refresh() {
            if (!!this.$refs.table) {
                const table = this.$refs.table;
                table.refreshData();
            }
        },
        async getRows(_, params) {
            if (!!this.isFiltering) {
                this.$params('loading', true);
            }

            const startRow = params.request.startRow;
            const endRow = params.request.endRow;

            // Get scope values
            const scopeValues = this.$refs.scope ? this.$refs.scope.getValues() : {};

            const result = await this.$rpc('sale.get-notify-when-available-report', {
                ...this.payload,
                ...scopeValues,
                categoryFilters: this.categoryFilters,
                module: this.module,
                limit: endRow - startRow,
                skip: startRow
            });

            if (!!this.isFiltering) {
                this.isFiltering = false;
                this.$params('loading', false);
            }

            this.rowCount = result.total;

            return result;
        },
        async summaryRow() {
            // Get scope values
            const scopeValues = this.$refs.scope ? this.$refs.scope.getValues() : {};

            const result = await this.$rpc('sale.get-total-notify-when-available-report', {
                ...this.payload,
                ...scopeValues,
                categoryFilters: this.categoryFilters,
                module: this.module
            });

            return {
                rowCount: this.rowCount,
                productDefinition: this.$t('TOTAL'),
                notifyRequestsCount: result.totalNotifyRequests,
                uniqueCustomersCount: result.totalUniqueCustomers,
                storeCount: result.totalUniqueProducts
            };
        },

        handleSelectCategory(selected) {
            const query = {$or: []};

            for (const category of selected) {
                query.$or.push({
                    'categoryPath': {
                        $regex: `^${escapeRegExp(category.path)}/`,
                        $options: 'i'
                    }
                });
                query.$or.push({
                    'categoryPath': {
                        $regex: `^${escapeRegExp(category.path)}$`,
                        $options: 'i'
                    }
                });
            }

            if (query.$or.length < 1) {
                delete query.$or;
            }

            this.isFiltering = true;
            this.categoryFilters = query;
            this.tableKey++;
        },
        handleScopeChange() {
            this.isFiltering = true;
            this.tableKey++;
        }
    }
};
</script>

<style lang="scss">
.sale-reports-notify-when-available {
    .sale-reports-notify-when-available-categories {
        .category-search {
            padding: 16px;
        }
    }
}
</style>
