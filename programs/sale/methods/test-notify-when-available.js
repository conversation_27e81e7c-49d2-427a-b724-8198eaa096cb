export default {
    name: 'test-notify-when-available',
    async action(payload, params) {
        const app = this.app;
        const user = params.user;

        // Check user
        if (!user || !user.isRoot) {
            throw new Error('Only root user can run this test!');
        }

        console.log('Creating test data for notify when available...');

        // Get a sample store
        const store = await app.collection('ecommerce.stores').findOne({
            $select: ['_id', 'name']
        });

        if (!store) {
            throw new Error('No ecommerce store found!');
        }

        console.log('Using store:', store.name, store._id);

        // Get a sample customer
        const customer = await app.collection('kernel.partners').findOne({
            type: 'customer',
            $select: ['_id', 'name']
        });

        if (!customer) {
            throw new Error('No customer found!');
        }

        console.log('Using customer:', customer.name, customer._id);

        // Get a sample product
        const product = await app.collection('inventory.products').findOne({
            type: 'stockable',
            $select: ['_id', 'code', 'name']
        });

        if (!product) {
            throw new Error('No product found!');
        }

        console.log('Using product:', product.name, product._id);

        // Create test notify when available record
        const testRecord = {
            storeId: store._id,
            customerId: customer._id,
            productId: product._id,
            isNotifyCustomer: true,
            isBuyLater: false,
            isAlarm: false
        };

        // Check if already exists
        const existing = await app.collection('store.collection-products').findOne({
            storeId: store._id,
            customerId: customer._id,
            productId: product._id,
            isNotifyCustomer: true
        });

        if (existing) {
            console.log('Test record already exists:', existing._id);
            return { message: 'Test record already exists', recordId: existing._id };
        }

        // Create the record
        const created = await app.collection('store.collection-products').create(testRecord, { user });
        
        console.log('Test record created:', created._id);

        // Verify creation
        const total = await app.collection('store.collection-products').count({
            isNotifyCustomer: true
        });

        console.log('Total notify when available records after creation:', total);

        return { 
            message: 'Test record created successfully', 
            recordId: created._id,
            totalRecords: total,
            testData: {
                store: store.name,
                customer: customer.name,
                product: product.name
            }
        };
    }
};
