import _ from 'lodash';

export default {
    name: 'get-favorite-products-report',
    async action(payload, params) {
        const app = this.app;
        const user = params.user;

        // Check user
        if (!_.isObject(user)) {
            throw new Error('User must be provided!');
        }

        // Get collection
        const collection = app.collection('store.products');

        // Get start and end date
        let endDate = app.datetime.local().toJSDate();
        let startDate = app.datetime.fromJSDate(endDate).minus({years: 1}).toJSDate();
        if (_.isDate(payload.endDate)) endDate = payload.endDate;
        if (_.isDate(payload.startDate)) startDate = payload.startDate;

        // Get branch ids
        let branchIds = [];
        if (_.isString(payload.branchId)) branchIds = [payload.branchId];
        if (Array.isArray(payload.branchId)) branchIds = payload.branchId;
        if (!(user.isRoot && !!app.setting('system.rootsAuthorizedOnAllBranches')) && branchIds.length < 1) {
            branchIds = user.branchIds;
        }

        // Get query
        let query = {
            favoritesCount: { $gt: 0 }
        };
        if (_.isObject(payload.query)) {
            query = { ...query, ...payload.query };
        }

        // Get limit and skip
        let limit = null;
        let skip = null;
        if (_.isInteger(payload.limit)) limit = payload.limit;
        if (_.isInteger(payload.skip)) skip = payload.skip;

        // Prepare match state
        const $match = {
            ...query
        };

        if (branchIds.length > 0) {
            $match.branchId = { $in: branchIds };
        }

        // Check permission
        if (!user.isRoot) {
            const permission = (user.recordPermissions || []).find(p => p.name === 'store.products');

            if (!_.isObject(permission)) {
                return { data: [], total: 0, skip, limit };
            }

            if (permission.read === 'no') {
                return { data: [], total: 0, skip, limit };
            } else if (permission.read === 'owned') {
                if (!Array.isArray($match.$and)) $match.$and = [];
                $match.$and.push({ createdBy: user._id });
            }
        }

        // Apply category filters if provided
        if (
            _.isPlainObject(payload.categoryFilters) &&
            Array.isArray(payload.categoryFilters.$or) &&
            payload.categoryFilters.$or.length > 0
        ) {
            if (!Array.isArray($match.$and)) $match.$and = [];
            $match.$and.push(payload.categoryFilters);
        }

        const pipeline = [
            { $match },
            {
                $project: {
                    productId: '$productId',
                    productCode: '$code',
                    productDefinition: '$name',
                    productType: '$type',
                    favoritesCount: 1,
                    categoryPath: 1
                }
            },
            { $sort: { favoritesCount: -1 } }
        ];

        // Get full report without pagination
        const fullReport = await collection.aggregate(pipeline);
        const totalCount = fullReport.length;

        let report = fullReport;
        if (!_.isNull(skip) && !_.isNull(limit)) {
            report = fullReport.slice(skip, skip + limit);
        }

        return {
            data: report,
            total: totalCount,
            skip,
            limit
        };
    }
};
