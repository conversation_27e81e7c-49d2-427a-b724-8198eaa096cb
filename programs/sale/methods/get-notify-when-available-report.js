import _ from 'lodash';

export default {
    name: 'get-notify-when-available-report',
    async action(payload, params) {
        const app = this.app;
        const user = params.user;

        // Check user
        if (!_.isObject(user)) {
            throw new Error('User must be provided!');
        }

        // Get collection
        const collection = app.collection('store.collection-products');

        // Get start and end date
        let endDate = app.datetime.local().toJSDate();
        let startDate = app.datetime.fromJSDate(endDate).minus({years: 1}).toJSDate();
        if (_.isDate(payload.endDate)) endDate = payload.endDate;
        if (_.isDate(payload.startDate)) startDate = payload.startDate;

        // Get store ids (e-ticaret mağaza filtreleri)
        let storeIds = [];
        if (_.isString(payload.storeId)) storeIds = [payload.storeId];
        if (Array.isArray(payload.storeId)) storeIds = payload.storeId;

        // Get branch ids (sale modülü için)
        let branchIds = [];
        if (_.isString(payload.branchId)) branchIds = [payload.branchId];
        if (Array.isArray(payload.branchId)) branchIds = payload.branchId;
        if (!(user.isRoot && !!app.setting('system.rootsAuthorizedOnAllBranches')) && branchIds.length < 1) {
            branchIds = user.branchIds;
        }

        // Get query
        let query = {
            isNotifyCustomer: true
        };
        if (_.isObject(payload.query)) {
            query = { ...query, ...payload.query };
        }

        // Get limit and skip
        let limit = null;
        let skip = null;
        if (_.isInteger(payload.limit)) limit = payload.limit;
        if (_.isInteger(payload.skip)) skip = payload.skip;

        // Prepare match state
        const $match = {
            ...query
        };

        // Add date filter only if dates are provided
        if (payload.startDate || payload.endDate) {
            $match.createdAt = {};
            if (payload.startDate) $match.createdAt.$gte = startDate;
            if (payload.endDate) $match.createdAt.$lte = endDate;
        }

        if (storeIds.length > 0) {
            $match.storeId = { $in: storeIds };
        }

        // Apply branch filter for sale module
        if (payload.module !== 'ecommerce' && branchIds.length > 0) {
            // For sale module, we need to join with products to get branch info
            // This will be handled in the aggregation pipeline
        }

        // Check permission
        if (!user.isRoot) {
            const permission = (user.recordPermissions || []).find(p => p.name === 'store.collection-products');

            if (!_.isObject(permission)) {
                return { data: [], total: 0, skip, limit };
            }

            if (permission.read === 'no') {
                return { data: [], total: 0, skip, limit };
            } else if (permission.read === 'owned') {
                if (!Array.isArray($match.$and)) $match.$and = [];
                $match.$and.push({ createdBy: user._id });
            }
        }

        const pipeline = [
            { $match },
            {
                $group: {
                    _id: '$productId',
                    productId: { $first: '$productId' },
                    notifyRequestsCount: { $sum: 1 },
                    storeIds: { $addToSet: '$storeId' },
                    customerIds: { $addToSet: '$customerId' },
                    firstRequestDate: { $min: '$createdAt' },
                    lastRequestDate: { $max: '$createdAt' }
                }
            },
            {
                $lookup: {
                    from: 'inventory.products',
                    localField: 'productId',
                    foreignField: '_id',
                    as: 'product'
                }
            },
            {
                $unwind: {
                    path: '$product',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: 'store.products',
                    localField: 'productId',
                    foreignField: 'productId',
                    as: 'storeProducts'
                }
            },
            {
                $project: {
                    productId: 1,
                    productCode: '$product.code',
                    productDefinition: '$product.name',
                    productType: '$product.type',
                    notifyRequestsCount: 1,
                    uniqueCustomersCount: { $size: '$customerIds' },
                    storeCount: { $size: '$storeIds' },
                    firstRequestDate: 1,
                    lastRequestDate: 1,
                    categoryPath: '$product.categoryPath',
                    storeProducts: {
                        $map: {
                            input: '$storeProducts',
                            as: 'sp',
                            in: {
                                storeId: '$$sp.storeId',
                                definition: '$$sp.definition',
                                slug: '$$sp.slug'
                            }
                        }
                    }
                }
            },
            { $sort: { notifyRequestsCount: -1 } }
        ];

        // Get full report without pagination
        console.log('Notify when available pipeline:', JSON.stringify(pipeline, null, 2));
        const fullReport = await collection.aggregate(pipeline);
        console.log('Notify when available results:', fullReport.length, 'records found');
        const totalCount = fullReport.length;

        let report = fullReport;
        if (!_.isNull(skip) && !_.isNull(limit)) {
            report = fullReport.slice(skip, skip + limit);
        }

        return {
            data: report,
            total: totalCount,
            skip,
            limit
        };
    }
};
