import _ from 'lodash';

export default {
    name: 'get-notify-when-available-simple',
    async action(payload, params) {
        const app = this.app;
        const user = params.user;

        // Check user
        if (!_.isObject(user)) {
            throw new Error('User must be provided!');
        }

        // Get collection
        const collection = app.collection('store.collection-products');

        // Get start and end date
        let endDate = app.datetime.local().toJSDate();
        let startDate = app.datetime.fromJSDate(endDate).minus({years: 1}).toJSDate();
        if (_.isDate(payload.endDate)) endDate = payload.endDate;
        if (_.isDate(payload.startDate)) startDate = payload.startDate;

        // Get store ids (e-ticaret mağaza filtreleri)
        let storeIds = [];
        if (_.isString(payload.storeId)) storeIds = [payload.storeId];
        if (Array.isArray(payload.storeId)) storeIds = payload.storeId;

        // Get query
        let query = {
            isNotifyCustomer: true
        };
        if (_.isObject(payload.query)) {
            query = { ...query, ...payload.query };
        }

        // Get limit and skip
        let limit = null;
        let skip = null;
        if (_.isInteger(payload.limit)) limit = payload.limit;
        if (_.isInteger(payload.skip)) skip = payload.skip;

        // Prepare match state
        const $match = {
            ...query
        };

        // Add date filter only if dates are provided
        if (payload.startDate || payload.endDate) {
            $match.createdAt = {};
            if (payload.startDate) $match.createdAt.$gte = startDate;
            if (payload.endDate) $match.createdAt.$lte = endDate;
        }

        if (storeIds.length > 0) {
            $match.storeId = { $in: storeIds };
        }

        console.log('Simple notify query:', JSON.stringify($match, null, 2));

        // Simple approach - just get the records without joins
        const records = await collection.find($match);
        
        console.log('Simple notify results:', records.length, 'records found');

        // Group by productId manually
        const grouped = {};
        for (const record of records) {
            const productId = record.productId;
            if (!grouped[productId]) {
                grouped[productId] = {
                    productId: productId,
                    productCode: 'Unknown',
                    productDefinition: 'Unknown Product',
                    productType: 'stockable',
                    notifyRequestsCount: 0,
                    uniqueCustomersCount: 0,
                    storeCount: 0,
                    firstRequestDate: record.createdAt,
                    lastRequestDate: record.createdAt,
                    customerIds: new Set(),
                    storeIds: new Set()
                };
            }
            
            grouped[productId].notifyRequestsCount++;
            grouped[productId].customerIds.add(record.customerId);
            grouped[productId].storeIds.add(record.storeId);
            
            if (record.createdAt < grouped[productId].firstRequestDate) {
                grouped[productId].firstRequestDate = record.createdAt;
            }
            if (record.createdAt > grouped[productId].lastRequestDate) {
                grouped[productId].lastRequestDate = record.createdAt;
            }
        }

        // Convert to array and calculate final counts
        const result = Object.values(grouped).map(item => ({
            ...item,
            uniqueCustomersCount: item.customerIds.size,
            storeCount: item.storeIds.size,
            customerIds: undefined,
            storeIds: undefined
        }));

        // Sort by request count
        result.sort((a, b) => b.notifyRequestsCount - a.notifyRequestsCount);

        // Apply pagination
        let paginatedResult = result;
        if (!_.isNull(skip) && !_.isNull(limit)) {
            paginatedResult = result.slice(skip, skip + limit);
        }

        console.log('Simple notify final result:', paginatedResult.length, 'items');

        return {
            data: paginatedResult,
            total: result.length,
            skip,
            limit
        };
    }
};
