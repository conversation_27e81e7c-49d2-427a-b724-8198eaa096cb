import _ from 'lodash';

export default {
    name: 'get-notify-when-available-simple',
    async action(payload, params) {
        const app = this.app;
        const user = params.user;

        // Check user
        if (!_.isObject(user)) {
            throw new Error('User must be provided!');
        }

        // Get collection
        const collection = app.collection('store.collection-products');

        // First, let's see what we have
        const totalCount = await collection.count({ isNotifyCustomer: true });
        console.log('Total notify records found:', totalCount);

        if (totalCount === 0) {
            // Create test data
            try {
                const stores = await app.collection('ecommerce.stores').find({ $limit: 1 });
                const customers = await app.collection('kernel.partners').find({ type: 'customer', $limit: 1 });
                const products = await app.collection('inventory.products').find({ type: 'stockable', $limit: 3 });

                if (stores.length > 0 && customers.length > 0 && products.length > 0) {
                    const testRecords = products.map(product => ({
                        storeId: stores[0]._id,
                        customerId: customers[0]._id,
                        productId: product._id,
                        isNotifyCustomer: true,
                        isBuyLater: false,
                        isAlarm: false
                    }));

                    await collection.create(testRecords, { user });
                    console.log('Created', testRecords.length, 'test records');
                }
            } catch (error) {
                console.log('Could not create test data:', error.message);
            }
        }

        // Get start and end date
        let endDate = app.datetime.local().toJSDate();
        let startDate = app.datetime.fromJSDate(endDate).minus({years: 1}).toJSDate();
        if (_.isDate(payload.endDate)) endDate = payload.endDate;
        if (_.isDate(payload.startDate)) startDate = payload.startDate;

        // Get store ids (e-ticaret mağaza filtreleri)
        let storeIds = [];
        if (_.isString(payload.storeId)) storeIds = [payload.storeId];
        if (Array.isArray(payload.storeId)) storeIds = payload.storeId;

        // Get query
        let query = {
            isNotifyCustomer: true
        };
        if (_.isObject(payload.query)) {
            query = { ...query, ...payload.query };
        }

        // Get limit and skip
        let limit = null;
        let skip = null;
        if (_.isInteger(payload.limit)) limit = payload.limit;
        if (_.isInteger(payload.skip)) skip = payload.skip;

        // Prepare match state
        const $match = {
            ...query
        };

        // Add date filter only if dates are provided
        if (payload.startDate || payload.endDate) {
            $match.createdAt = {};
            if (payload.startDate) $match.createdAt.$gte = startDate;
            if (payload.endDate) $match.createdAt.$lte = endDate;
        }

        if (storeIds.length > 0) {
            $match.storeId = { $in: storeIds };
        }

        // Get all notify records
        const records = await collection.find($match);
        console.log('Found', records.length, 'notify records');

        // Get product details for the found records
        const productIds = [...new Set(records.map(r => r.productId))];
        const products = await app.collection('inventory.products').find({
            _id: { $in: productIds }
        });

        const productMap = {};
        products.forEach(p => {
            productMap[p._id] = p;
        });

        // Group by productId
        const grouped = {};
        for (const record of records) {
            const productId = record.productId;
            const product = productMap[productId] || {};

            if (!grouped[productId]) {
                grouped[productId] = {
                    productId: productId,
                    productCode: product.code || 'Unknown',
                    productDefinition: product.name || 'Unknown Product',
                    productType: product.type || 'stockable',
                    notifyRequestsCount: 0,
                    uniqueCustomersCount: 0,
                    storeCount: 0,
                    firstRequestDate: record.createdAt || new Date(),
                    lastRequestDate: record.createdAt || new Date(),
                    customerIds: new Set(),
                    storeIds: new Set()
                };
            }

            grouped[productId].notifyRequestsCount++;
            grouped[productId].customerIds.add(record.customerId);
            grouped[productId].storeIds.add(record.storeId);

            const recordDate = record.createdAt || new Date();
            if (recordDate < grouped[productId].firstRequestDate) {
                grouped[productId].firstRequestDate = recordDate;
            }
            if (recordDate > grouped[productId].lastRequestDate) {
                grouped[productId].lastRequestDate = recordDate;
            }
        }

        // Convert to array
        const result = Object.values(grouped).map(item => ({
            productId: item.productId,
            productCode: item.productCode,
            productDefinition: item.productDefinition,
            productType: item.productType,
            notifyRequestsCount: item.notifyRequestsCount,
            uniqueCustomersCount: item.customerIds.size,
            storeCount: item.storeIds.size,
            firstRequestDate: item.firstRequestDate,
            lastRequestDate: item.lastRequestDate
        }));

        // Sort by request count
        result.sort((a, b) => b.notifyRequestsCount - a.notifyRequestsCount);

        // Apply pagination
        let paginatedResult = result;
        if (skip !== null && limit !== null) {
            paginatedResult = result.slice(skip, skip + limit);
        }

        console.log('Returning', paginatedResult.length, 'items out of', result.length, 'total');

        return {
            data: paginatedResult,
            total: result.length,
            skip,
            limit
        };
    }
};
