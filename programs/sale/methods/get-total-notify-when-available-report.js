import _ from 'lodash';

export default {
    name: 'get-total-notify-when-available-report',
    async action(payload, params) {
        const app = this.app;
        const user = params.user;

        // Check user
        if (!_.isObject(user)) {
            throw new Error('User must be provided!');
        }

        // Get collection
        const collection = app.collection('store.collection-products');

        // Get start and end date
        let endDate = app.datetime.local().toJSDate();
        let startDate = app.datetime.fromJSDate(endDate).minus({years: 1}).toJSDate();
        if (_.isDate(payload.endDate)) endDate = payload.endDate;
        if (_.isDate(payload.startDate)) startDate = payload.startDate;

        // Get store ids (e-ticaret mağaza filtreleri)
        let storeIds = [];
        if (_.isString(payload.storeId)) storeIds = [payload.storeId];
        if (Array.isArray(payload.storeId)) storeIds = payload.storeId;

        // Get query
        let query = {
            isNotifyCustomer: true
        };
        if (_.isObject(payload.query)) {
            query = { ...query, ...payload.query };
        }

        // Prepare match state
        const $match = {
            ...query,
            createdAt: {
                $gte: startDate,
                $lte: endDate
            }
        };

        if (storeIds.length > 0) {
            $match.storeId = { $in: storeIds };
        }

        // Check permission
        if (!user.isRoot) {
            const permission = (user.recordPermissions || []).find(p => p.name === 'store.collection-products');

            if (!_.isObject(permission)) {
                return { totalNotifyRequests: 0, totalUniqueProducts: 0, totalUniqueCustomers: 0 };
            }

            if (permission.read === 'no') {
                return { totalNotifyRequests: 0, totalUniqueProducts: 0, totalUniqueCustomers: 0 };
            } else if (permission.read === 'owned') {
                if (!Array.isArray($match.$and)) $match.$and = [];
                $match.$and.push({ createdBy: user._id });
            }
        }

        const pipeline = [
            { $match },
            {
                $group: {
                    _id: null,
                    totalNotifyRequests: { $sum: 1 },
                    uniqueProducts: { $addToSet: '$productId' },
                    uniqueCustomers: { $addToSet: '$customerId' }
                }
            },
            {
                $project: {
                    totalNotifyRequests: 1,
                    totalUniqueProducts: { $size: '$uniqueProducts' },
                    totalUniqueCustomers: { $size: '$uniqueCustomers' }
                }
            }
        ];

        const result = await collection.aggregate(pipeline);

        return {
            totalNotifyRequests: result.length > 0 ? result[0].totalNotifyRequests : 0,
            totalUniqueProducts: result.length > 0 ? result[0].totalUniqueProducts : 0,
            totalUniqueCustomers: result.length > 0 ? result[0].totalUniqueCustomers : 0
        };
    }
};
