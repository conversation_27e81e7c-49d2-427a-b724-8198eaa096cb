export default [
    {
        name: 'sales',
        title: 'Sales',
        icon: 'chart-line',
        items: [
            {
                name: 'orders',
                title: 'Orders',
                params: {module: 'ecommerce', filters: {module: 'ecommerce'}},
                view: 'sale.sales.orders.master'
            },
            {
                name: 'orders-detail',
                params: {model: {module: 'ecommerce'}},
                view: 'sale.sales.orders.detail'
            },
            {
                name: 'invoices',
                title: 'Invoices',
                params: {module: 'ecommerce', filters: {module: 'ecommerce'}},
                view: 'accounting.sales.customer-invoices.master'
            },
            {
                name: 'invoices-detail',
                params: {model: {module: 'ecommerce'}},
                view: 'accounting.sales.customer-invoices.detail'
            },
            {
                name: 'cancellations',
                title: 'Cancellations',
                params: {module: 'ecommerce'},
                view: 'sale.sales.cancellations.master'
            },
            {
                name: 'returns',
                title: 'Returns',
                params: {module: 'ecommerce', filters: {module: 'ecommerce'}},
                view: 'sale.sales.returns.master'
            },
            {
                name: 'returns-detail',
                params: {model: {module: 'ecommerce'}},
                view: 'sale.sales.returns.detail'
            },
            {
                name: 'to-invoice',
                title: 'To Invoice',
                params: {module: 'ecommerce'},
                view: 'sale.sales.to-invoice.master'
            },
            {
                name: 'deliveries',
                title: 'Deliveries',
                params: {module: 'ecommerce'},
                view: 'sale.sales.deliveries.master'
            },
            {name: 'stores', title: 'Stores'},
            {name: 'stores-detail'}
        ]
    },

    // Customer Relations
    {
        name: 'customer-relations',
        title: 'Customer Relations',
        icon: 'handshake',
        items: [
            {
                name: 'customers',
                title: 'Customers',
                view: 'partners.partners.master',
                async params() {
                    const app = this.app;
                    const stores = await app.collection('ecommerce.stores').find({
                        $select: ['customerGroupId']
                    });

                    return {
                        filters: {
                            type: 'customer',
                            groupId: {
                                $in: stores.filter(s => !!s.customerGroupId).map(s => s.customerGroupId)
                            }
                        }
                    };
                }
            },
            {
                name: 'customers-detail',
                view: 'partners.partners.detail',
                params: {model: {type: 'customer'}}
            },
            {
                name: 'leads',
                title: 'Leads',
                view: 'crm.customer-relations.leads.master'
            },
            {
                name: 'leads-detail',
                view: 'crm.customer-relations.leads.detail'
            },
            {
                name: 'opportunities',
                title: 'Opportunities',
                view: 'crm.customer-relations.opportunities.master'
            },
            {
                name: 'opportunities-detail',
                view: 'crm.customer-relations.opportunities.detail'
            },
            {
                name: 'competitors',
                title: 'Competitors',
                view: 'crm.customer-relations.competitors.master'
            },
            {
                name: 'competitors-detail',
                view: 'crm.customer-relations.competitors.detail'
            }
        ]
    },

    // Catalog
    {
        name: 'catalog',
        title: 'Catalog',
        icon: 'books',
        items: [
            {
                name: 'products',
                title: 'Products',
                view: 'inventory.catalog.products.master',
                params: {filters: {canBeSold: true, isECommerceProduct: true}}
            },
            {
                name: 'products-detail',
                view: 'inventory.catalog.products.detail',
                params: {model: {canBeSold: true, isECommerceProduct: true}}
            },
            {
                name: 'configurable-products',
                title: 'Configurable Products',
                view: 'inventory.catalog.products.master',
                params: {
                    filters: {
                        isConfigurable: true,
                        canBeSold: true,
                        isECommerceProduct: true
                    }
                }
            },
            {
                name: 'configurable-products-detail',
                view: 'inventory.catalog.products.detail',
                params: {
                    model: {
                        isConfigurable: true,
                        isSimple: false,
                        canBeSold: true,
                        isECommerceProduct: true
                    }
                }
            },
            {
                name: 'kit-products',
                title: 'Kit Products',
                view: 'inventory.catalog.products.master',
                params: {
                    filters: {
                        isKit: true,
                        canBeSold: true,
                        isECommerceProduct: true
                    }
                }
            },
            {
                name: 'kit-products-detail',
                view: 'inventory.catalog.products.detail',
                params: {
                    model: {
                        isKit: true,
                        isSimple: false,
                        canBeSold: true,
                        isECommerceProduct: true
                    }
                }
            },
            {
                name: 'serial-numbers',
                title: 'Serial Numbers',
                async condition(app) {
                    return app.setting('inventory.trackBySerialOrLot');
                }
            },
            {name: 'serial-numbers-detail'},
            {
                name: 'lot-numbers',
                title: 'Lot Numbers',
                async condition(app) {
                    return app.setting('inventory.trackBySerialOrLot');
                }
            },
            {name: 'lot-numbers-detail'}
        ]
    },

    // Prices.
    {
        name: 'pricing',
        title: 'Pricing',
        icon: 'coins',
        items: [
            {
                name: 'price-lists',
                title: 'Price Lists',
                async condition(app) {
                    return app.setting('sale.salesPriceList');
                },
                params: {
                    filters: {module: 'ecommerce'},
                    module: 'ecommerce'
                },
                view: 'sale.pricing.price-lists.master'
            },
            {
                name: 'price-lists-detail',
                params: {model: {module: 'ecommerce'}},
                view: 'sale.pricing.price-lists.detail'
            },
            {
                name: 'campaigns',
                title: 'Campaigns',
                params: {
                    filters: {module: 'ecommerce'},
                    module: 'ecommerce'
                },
                view: 'sale.pricing.campaigns.master'
            },
            {
                name: 'campaigns-detail',
                params: {
                    model: {
                        module: 'ecommerce',
                        scope: ['ecommerce']
                    }
                },
                view: 'sale.pricing.campaigns.detail'
            },
            {
                name: 'campaign-groups',
                title: 'Campaign Groups',
                view: 'sale.pricing.campaign-groups.master'
            },
            {
                name: 'campaign-groups-detail',
                view: 'sale.pricing.campaign-groups.detail'
            },
            {
                name: 'discount-lists',
                title: 'Discount Lists',
                async condition(app) {
                    return app.setting('sale.lineDiscounts');
                },
                params: {filters: {module: 'ecommerce'}},
                view: 'sale.pricing.discount-lists.master'
            },
            {
                name: 'discount-lists-detail',
                params: {model: {module: 'ecommerce'}},
                view: 'sale.pricing.discount-lists.detail'
            },
            {
                name: 'general-discount-lists',
                title: 'General Discount Lists',
                async condition(app) {
                    return app.setting('sale.discounts');
                },
                params: {filters: {module: 'ecommerce'}},
                view: 'sale.pricing.general-discount-lists.master'
            },
            {
                name: 'general-discount-lists-detail',
                params: {model: {module: 'ecommerce'}},
                view: 'sale.pricing.general-discount-lists.detail'
            },
            {
                name: 'sellout',
                title: 'Sellout',
                async condition(app) {
                    return app.setting('sale.sellout');
                },
                params: {
                    module: 'ecommerce',
                    filters: {
                        $or: [
                            {module: 'ecommerce'},
                            {module: {$eq: null}},
                            {module: {$eq: ''}},
                            {module: {$exists: false}}
                        ]
                    }
                },
                view: 'sale.pricing.sellout.master'
            },
            {name: 'sellout-detail', params: {model: {module: 'ecommerce'}}, view: 'sale.pricing.sellout.detail'},
            {
                name: 'pricing-wizard',
                title: 'Pricing Wizard',
                async condition(app) {
                    return app.setting('sale.salesPriceList');
                },
                params: {
                    filters: {module: 'ecommerce'},
                    module: 'ecommerce'
                },
                view: 'sale.pricing.pricing-wizard.master'
            }
        ]
    },

    // Reports
    {
        name: 'reports',
        title: 'Reports',
        icon: 'chart-bar',
        items: [
            {name: 'abandoned-cart', title: 'Abandoned Cart'},
            {name: 'abandoned-cart-detail'},
            {name: 'live-view', title: 'Live View'},
            {
                name: 'best-selling-products',
                title: 'Best Selling Products',
                view: 'sale.reports.best-selling-products',
                params: {filters: {module: 'ecommerce'}}
            },
            {
                name: 'favorite-products',
                title: 'Favorite Products',
                view: 'sale.reports.favorite-products',
                params: {filters: {module: 'ecommmerce'}}
            }
        ]
    },

    // Configuration
    {
        name: 'configuration',
        title: 'Configuration',
        icon: 'wrench',
        items: [
            {name: 'integrations', title: 'Integrations'},
            {name: 'integrations-detail'},
            {name: 'fixed-exchange-rates', title: 'Fixed Exchange Rates'},
            {
                name: 'return-reasons',
                title: 'Return Reasons',
                view: 'system.management.configuration.return-reasons.master'
            }
        ]
    }

    // Settings
    // {name: 'settings', title: 'Settings', icon: 'cog', params: {isPreview: true}}
];
