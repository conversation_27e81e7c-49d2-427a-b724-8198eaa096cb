import _ from 'lodash';

export default {
    name: 'notify-when-available',
    title: 'Notify When Available',
    collection: 'store.collection-products',
    fields: [
        {
            type: 'string',
            name: 'storeId',
            label: 'Store ID',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'customerId',
            label: 'Customer ID',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'productId',
            label: 'Product ID',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'productCode',
            label: 'Product Code',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'productName',
            label: 'Product Name',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'productType',
            label: 'Product Type',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'storeName',
            label: 'Store Name',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'storeCode',
            label: 'Store Code',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'customerName',
            label: 'Customer Name',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'customerEmail',
            label: 'Customer Email',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'customerCode',
            label: 'Customer Code',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'customerPhone',
            label: 'Customer Phone',
            searchable: true,
            filterable: true
        },
        {
            type: 'date',
            name: 'requestedAt',
            label: 'Requested At',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'city',
            label: 'City',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'district',
            label: 'District',
            searchable: true,
            filterable: true
        },
    ],
    attributes: {
        store: {
            collection: 'ecommerce.stores',
            parentField: 'storeId',
            childField: '_id'
        },
        customer: {
            collection: 'kernel.partners', 
            parentField: 'customerId',
            childField: '_id'
        },
        product: {
            collection: 'inventory.products',
            parentField: 'productId',
            childField: '_id'
        }
    },
    
    async preload(app, _, documents) {
        const storeIds = _.uniq(documents.map(d => d.storeId).filter(Boolean));
        const customerIds = _.uniq(documents.map(d => d.customerId).filter(Boolean));
        const productIds = _.uniq(documents.map(d => d.productId).filter(Boolean));

        const [stores, customers, products] = await Promise.all([
            storeIds.length > 0 ? app.collection('ecommerce.stores').find({
                _id: {$in: storeIds},
                $select: ['_id', 'name', 'code']
            }) : [],
            customerIds.length > 0 ? app.collection('kernel.partners').find({
                _id: {$in: customerIds},
                $select: ['_id', 'name', 'email', 'code', 'phone', 'address']
            }) : [],
            productIds.length > 0 ? app.collection('inventory.products').find({
                _id: {$in: productIds},
                $select: ['_id', 'name', 'code', 'type']
            }) : []
        ]);

        const storeMap = _.keyBy(stores, '_id');
        const customerMap = _.keyBy(customers, '_id');
        const productMap = _.keyBy(products, '_id');

        return {storeMap, customerMap, productMap};
    },

    async data(_, __, document, {storeMap, customerMap, productMap}) {
        const data = {};

        const store = storeMap[document.storeId] || {};
        const customer = customerMap[document.customerId] || {};
        const product = productMap[document.productId] || {};
        const address = customer?.address || {};

        data.requestedAt = document.createdAt;

        data.storeId = document.storeId;
        data.storeName = store.name;
        data.storeCode = store.code;

        data.customerId = document.customerId;
        data.customerName = customer.name;
        data.customerEmail = customer.email;
        data.customerCode = customer.code;
        data.customerPhone = customer.phone;
        data.city = address.city || "";
        data.district = address.district || "";

        data.productId = document.productId;
        data.productName = product.name;
        data.productCode = product.code;
        data.productType = product.type;

        return data;
    }
};
