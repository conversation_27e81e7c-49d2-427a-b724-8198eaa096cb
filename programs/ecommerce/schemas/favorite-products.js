import _ from 'lodash';

export default {
    name: 'favorite-products',
    title: 'Favorite Products',
    collection: 'store.favorite-products',
    fields: [
        {
            type: 'string',
            name: 'storeId',
            label: 'Store ID',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'customerId',
            label: 'Customer ID',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'productId',
            label: 'Product ID',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'mainProductId',
            label: 'Main Product ID', 
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'productCode',
            label: 'Product Code',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'productName',
            label: 'Product Name',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'storeCode',
            label: 'Store Code',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'storeName',
            label: 'Store Name',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'customerCode',
            label: 'Customer Code',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'customerName',
            label: 'Customer Name',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'customerEmail',
            label: 'Customer Email',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'customerPhone',
            label: 'Customer Phone',
            searchable: true,
            filterable: true
        },
        {
            type: 'date',
            name: 'addedAt',
            label: 'Added At',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'city',
            label: 'City',
            searchable: true,
            filterable: true
        },
        {
            type: 'string',
            name: 'district',
            label: 'District',
            searchable: true,
            filterable: true
        },
    ],
    attributes: {
        store: {
            collection: 'store.stores',
            parentField: 'storeId',
            childField: '_id'
        },
        customer: {
            collection: 'store.customers', 
            parentField: 'customerId',
            childField: '_id'
        },
        product: {
            collection: 'store.products',
            parentField: 'productId',
            childField: 'productId'
        }
    },
    async bulkDocumentExtra(app, schema, documents) {
        const productMap = {};
        const storeMap = {};
        const customerMap = {};

        const productIds = [];
        const storeIds = [];
        const customerIds = [];

        for (const document of documents) {
            if (document.productId) productIds.push(document.productId);
            if (document.storeId) storeIds.push(document.storeId);
            if (document.customerId) customerIds.push(document.customerId);
        }

        if (productIds.length > 0) {
            const products = await app.collection('inventory.products').find({
                _id: {$in: _.uniq(productIds)},
                $select: ['_id', 'code', 'name', 'variants'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            for (const product of products) {
                productMap[product._id] = product;
            }
        }

        if (storeIds.length > 0) {
            const stores = await app.collection('ecommerce.stores').find({
                _id: {$in: _.uniq(storeIds)},
                $select: ['name', 'code'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const store of stores) {
                storeMap[store._id] = store;
            }
        }

        if (customerIds.length > 0) {
            const customers = await app.collection('kernel.partners').find({
                _id: {$in: _.uniq(customerIds)},
                $select: ['name', 'email', 'code', 'phone', 'address'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const customer of customers) {
                customerMap[customer._id] = customer;
            }
        }

        return {productMap, storeMap, customerMap};
    },

    async data(app, schema, document, {productMap, storeMap, customerMap}) {
        const data = {};

        const product = productMap[document.productId] || {};
        const store = storeMap[document.storeId] || {};
        const customer = customerMap[document.customerId] || {};
        const address = customer?.address || {};

        data.addedAt = document.createdAt;

        data.storeId = document.storeId;
        data.storeName = store.name;
        data.storeCode = store.code;

        data.customerId = document.customerId;
        data.customerName = customer.name;
        data.customerEmail = customer.email;
        data.customerCode = customer.code;
        data.customerPhone = customer.phone;
        data.city = address.city || "";
        data.district = address.district || "";

        data.productId = document.productId;
        data.productCode = product.code;
        data.productName = product.name;

        if (product.variants && Array.isArray(product.variants)) {
            const variant = product.variants.find(v => v._id === document.productId);
            if (variant) {
                data.productCode = variant.code;
                data.productName = variant.name;
            }
        }

        return data;
    }
};

